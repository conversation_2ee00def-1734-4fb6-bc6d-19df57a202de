{{#
    @name Perspectives Index
#}}

<!-- /case_studies/index.antlers.html -->
{{ if use_dark_header_theme }}
	{{ partial:layout/header theme="dark" }}
{{ else }}
	{{ partial:layout/header theme="light" }}
{{ /if }}

<div class="mt-10">
	<section class="c-text-feature rounded-t-lg bg-white z-30 relative snap-start ">
		<div class="container px-4">
			<div class="grid grid-cols-12">
				<div class="col-span-1 text-right">
					<div class="c-label"></div>
				</div>
				<div class="col-span-11 lg:col-span-7">
					<h1 class="flex font-bold text-48 lg:text-64">
						A look at some of<br>our work.
					</h1>
				</div>
			</div>
			<div class="grid grid-cols-12 mb-12">
				<div class="col-start-2 col-span-10 2xl:col-start-2 2xl:col-span-5">
					<div class="text-24 font-semibold mt-7"><p class="mb-6">At Platinum Seed, we're focused on creating strategic work that resonates with our clients’ customers.</p>
					<p>Always seeking long lasting relationships, delivering measurable ROI and mutual growth with like-minded businesses.</p></div>
				</div>
			</div>
		</div>
	</section>

	{{ page_builder scope="block" }}
		{{ partial src="page_builder/{type}" }}
	{{ /page_builder }}
</div>

{{ section:index_content }}
	{{ collection:case_studies sort="date:desc" as="items" }}
		<section class="container px-4 mb-18">
			<div class="grid grid-cols-12">
				<div class="col-start-2 col-span-10">
					<div class="c-tag-filter flex flex-wrap gap-4 pb-5 border-b border-dark mb-9">
						<div>{{ partial:atoms/tag url="/our-work/" label="All Featured" selected="true" }}</div>
						{{ taxonomy from="case_study_categories" }}
							<div>{{ partial:atoms/tag url="/our-work/case-study-categories/{{slug}}/" :label="title" }}</div>
						{{ /taxonomy }}
					</div>
					<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 -mx-7">
						{{ unless no_results }}
							{{ items }}
								{{ partial:components/our-work-card bg="bg-{{colour_scheme}}-accent" as='a' :card_url="url" cardbg="bg-white" :image="listing_image:url" :title="hero_title" :brand="title" }}
							{{ /items }}
						{{ else }}
							<div class="lg:col-span-2">
								{{ trans:strings.no_results }}
							</div>
						{{ /unless }}
					</div>
				</div>	
			</div>
		</section>
	{{ /collection:case_studies }}
{{ /section:index_content }}
<!-- End: /case_studies/index.antlers.html -->