{{#
    @name Perspectives by category Index
#}}

<!-- /perspective/perspectives_categories/index.antlers.html -->
{{ if use_dark_header_theme }}
	{{ partial:layout/header theme="dark" }}
{{ else }}
	{{ partial:layout/header theme="light" }}
{{ /if }}

<div class="mt-10">
	<section class="c-text-feature rounded-t-lg bg-white z-30 relative snap-start ">
		<div class="container px-4">
			<div class="grid grid-cols-12">
				<div class="col-span-1 text-right">
					<div class="c-label"></div>
				</div>
				<div class="col-span-11 lg:col-span-7">
					<div class="flex font-bold text-48 lg:text-64">
						A look at some of<br>our work. {{ test }}
					</div>
				</div>
			</div>
			<div class="grid grid-cols-12 mb-12">
				<div class="col-start-2 col-span-10 2xl:col-start-2 2xl:col-span-5">
					<div class="text-24 font-semibold mt-7">
						<h1 class="mb-6 font-bold">{{ title }}</h1>
						{{ if content }}
							<div class="font-semibold">{{ content }}</div>
						{{ else }}
							<p class="font-semibold">Always seeking long lasting relationships, delivering measurable ROI and mutual growth with like-minded businesses.</p>
						{{ /if }}
				</div>
			</div>
		</div>
	</section>

	{{ entries as="items" }}
		<div class="container px-4">
			<div class="grid grid-cols-12 pb-19">
				<div class="col-start-2 col-span-10">
					<div class="c-tag-filter flex flex-wrap gap-4 pb-5 border-b border-dark mb-9">
						{{ partial:atoms/tag url="/our-work/" label="All Featured" }}
						{{ filtered_tags }}
							{{ if page:slug == slug }}
								<div>{{ partial:atoms/tag url="/our-work/tags/{{slug}}/" :label="title" selected="true" }}</div>
							{{ else }}
								<div>{{ partial:atoms/tag url="/our-work/tags/{{slug}}/" :label="title" }}</div>
							{{ /if }}
						{{ /filtered_tags }}
					</div>
					<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 -mx-7">
						{{ unless no_results }}
							{{ items }}
								{{ partial:components/our-work-card :bg="card_bg" as='a' :card_url="url" cardbg="bg-white" :image="listing_image:url" :title="hero_title" :brand="title" }}
							{{ /items }}
						{{ else }}
							<div class="md:col-span-6">
								{{ trans:strings.no_results }}
							</div>
						{{ /unless }}
					</div>
				</div>
			</div>
		</div>
	{{ /entries }}
</div>
<!-- End: /perspective/index.antlers.html -->