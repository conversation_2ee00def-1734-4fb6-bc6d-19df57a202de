{{#
    @name Home
    @desc Home page template, excludes header
#}}



<!-- /home.antlers.html pt-9 -->
<div id="fullpage" class="js-homepage">
	<!-- Desktop header -->
	<section class="section js-first-panel">
		<div class="sticky top-0 left-0">
			<div class="content bg-dark h-screen relative z-10">
				{{ partial:layout/header theme="dark" class="top-0 left-0 right-0 z-20 absolute" }}
				<div class="container px-4 mx-auto h-screen flex justify-center flex-col">
					<h1 id="jsDemo" class="text-[clamp(50px,_6.5vw,_150px)] font-bold text-white leading-[1.3] mb-2">
						<div class="min-[540px]:ml-[10%]">16 years</div>
						<div class="">in the <span class="whitespace-no-wrap inline-block">[<span class="text-accent js-random-term"></span>]</span></div>
						<div class="min-[540px]:ml-[20%]">game /</div>
					</h1>
					<div class="text-hero-sub font-bold mt-6 min-[540px]:ml-[52%]"><span class="text-grey-200">Shows that we're not playing around.</span></div>
				</div>
			</div>
		</div>

		<div class="js-home-intro bg-white rounded-t-lg relative h-screen z-20 shadow-[0px_0px_1px_rgba(12,26,75,0.08),0px_-20px_16px_rgba(20,37,63,0.03)] flex flex-col justify-center">
			<div class="container px-4 py-11 sm:px-0 h-full">
				<div class="flex flex-col justify-between h-full">
					{{ partial:typography/label maxwidth="lg:col-span-10" }}
						We are a trusted independent digital marketing agency helping businesses create valuable online experiences. 
					{{ /partial:typography/label }}
					<div class="grid grid-cols-12 lg:mt-10">
						<div class="col-start-2 col-span-10 lg:col-start-2 h-[1px] bg-dark my-2"></div>
						<div class="col-start-2 col-span-10 lg:col-start-2">
							<p class="text-36 sm:text-48 [@media(max-height:671px)]:!text-48 xl:text-56">Our strategically imaginative approach turns ideas into digital opportunities for brand growth.</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section class="section mb-12 md:mb-0">
		<div class="js-our-work-slider swiper swiper-container h-screen" data-autoadvance="true">
			<div class="swiper-wrapper transition-all duration-300 ease-linear">
				<div class="swiper-slide py-8 slide1 js-swiper-slide-0 w-screen min-h-full md:rounded-l-lg bg-white">
					<div class="container pl-7 pr-0 sm:px-0 md:pl-7 mx-auto mr-0 h-screen md:h-full flex justify-start items-center">
						<div class="w-full flex flex-col justify-between">
							<div class="mb-4 md:mb-10">
								<div class="js-slide-text transition-all duration-300 xl:px-0">
									<div class="text-18 sm:text-24 font-semibold mb-3">Platinum Seed</div>
									<div class="text-32 sm:text-48 xl:text-64 font-bold">Some of our work:</div>
								</div>
							</div>
							<div class="shrink opacity-0 aspect-[3]">

							</div>
						</div>
					</div>
				</div>
				<!-- bg-lovepup-bold bg-nua-bold bg-easycar-bold -->
				{{ featured_case_studies:home_slider_case_studies }}
					<a href="{{ url | ensure_right('/') }}" class="swiper-slide js-swiper-slide-{{ index + 1 }} block py-8 md:rounded-l-lg bg-{{ colour_scheme }}-bold js-view-case-cursor {{ if last }} js-last-slide {{ /if }}" data-bg="bg-{{ colour_scheme }}-accent">
						<div class="container pl-7 pr-0 sm:px-0 md:pl-7 mx-auto mr-0 h-screen md:h-full flex justify-start items-center">
							<div class="w-full flex flex-col justify-between">
								<div class="mb-4 md:mb-10">
									<div class="js-slide-text js-slide-text-1 text-white transition-all duration-300">
										<div class="text-18 sm:text-24 font-semibold mb-3">{{ title }}</div>
										<div class="text-32 sm:text-48 xl:text-64 font-bold">{{ hero_title }}</div>
										{{ partial:components/button as="span" theme="dark" class="xl:hidden mt-3 md:mt-6" type="line" label="VIEW CASE" }}
									</div>
								</div>
								<div class="shrink">
									{{ responsive:feature_image class="w-full" :alt="title" }}
								</div>
							</div>
						</div>
					</a>
				{{ /featured_case_studies:home_slider_case_studies }}
			</div>
		</div>
	</section>

	{{ page_builder scope="block" }} 
		{{ partial src="page_builder/accordion" }}
	{{ /page_builder }}

	<section class="section rounded-t-lg bg-white">
		<div class="bg-white md:h-screen relative z-20 flex flex-col justify-center mb-12">
			<div class="container px-4 py-13 md:py-0">
				<div class="grid grid-cols-12">

				</div>
				{{ partial:typography/label maxwidth="lg:col-span-10 2xl::col-span-8" }}
					Results-driven digital marketing tailored to your brand.
				{{ /partial:typography/label }}
			</div>
		</div>

		{{ partial:layout/footer }}
	</section>

	
</div>


<script>
	const terms = [
		{{ industry_terms:terms }}
			'{{ value }}',
		{{ /industry_terms:terms }}
	];
	// Get the element that will display the terms
const termElement = document.querySelector('.js-random-term');

// Counter to track how many times we've changed the term
let changeCount = 0;

// Function to update the term with a random value
function updateTerm() {
	changeCount++;
	
	// Every 5th change, set the term to "digital"
	if (changeCount % 5 === 0) {
	  termElement.textContent = 'digital';
	} else {
	  // Otherwise, pick a random term from the array
	  const randomIndex = Math.floor(Math.random() * terms.length);
	  termElement.textContent = terms[randomIndex];
	}
  }

// Set initial term without animation
changeCount++;
if (changeCount % 5 === 0) {
  termElement.textContent = 'digital';
} else {
  const randomIndex = Math.floor(Math.random() * terms.length);
  termElement.textContent = terms[randomIndex];
}

// Update the term every 3 seconds (3000 milliseconds)
// Adding 1000ms for the full animation cycle (500ms fade out + 500ms fade in)
setInterval(updateTerm, 1200);
</script>
<!-- End: /home.antlers.html -->
