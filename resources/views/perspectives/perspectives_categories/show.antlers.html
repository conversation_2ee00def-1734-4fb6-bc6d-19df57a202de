{{#
    @name Perspectives by category Index
#}}

<!-- /perspective/perspectives_categories/index.antlers.html -->
{{ if use_dark_header_theme }}
	{{ partial:layout/header theme="dark" }}
{{ else }}
	{{ partial:layout/header theme="light" }}
{{ /if }}

<div class="mt-10">
	<section class="c-text-feature rounded-t-lg bg-white z-30 relative snap-start ">
		<div class="container px-4">
			<div class="grid grid-cols-12">
				<div class="col-span-1 text-right">
					<div class="c-label"></div>
				</div>
				<div class="col-span-11 lg:col-span-7">
					<div class="flex font-bold text-36 sm:text-48 xl:text-64">
						These are our<br />Perspectives.
					</div>
				</div>
			</div>
			<div class="grid grid-cols-12 mb-12">
				<div class="col-start-2 col-span-10 2xl:col-start-2 2xl:col-span-5">
					<div class="text-24 mt-7">
						<h1 class="mb-6 font-bold">{{ title }}</h1>
						{{ if content }}
							<div class="font-semibold">{{ content }}</div>
						{{ else }}
							<p class="font-semibold">Always seeking long lasting relationships, delivering measurable ROI and mutual growth with like-minded businesses.</p>
						{{ /if }}
					</div>
				</div>
			</div>
		</div>
	</section>

	{{ entries as="items" }}
		<div class="container px-4">
			<div class="grid grid-cols-12 pb-19">
				<div class="col-start-2 col-span-10">
					<div class="c-tag-filter flex flex-wrap gap-4 pb-5 border-b border-dark mb-9">
						{{ partial:atoms/tag url="/perspectives/" label="All Featured" }}
						{{ taxonomy from="perspectives_categories" collection="perspectives" }}
							{{ if page:slug == slug }}
								<div>{{ partial:atoms/tag url="/perspectives/perspectives-categories/{{slug}}/" :label="title" selected="true" }}</div>
							{{ else }}
								<div>{{ partial:atoms/tag url="/perspectives/perspectives-categories/{{slug}}/" :label="title" }}</div>
							{{ /if }}
						{{ /taxonomy }}
					</div>
					<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
						{{ unless no_results }}
							{{ items }}
								{{ if count == 1 || count == 7}}
									{{ partial:components/blog-card :url="url" with_image="true" theme="dark" :image="feature_image" :tag="page:title" :title="title" :excerpt="excerpt" class="col-span-1 md:col-span-2" }}
								{{ else }}
									{{ partial:components/blog-card :url="url" theme="dark" :image="feature_image" :tag="page:title" :title="title" :excerpt="excerpt" class="col-span-1 md:col-span-2" }}
								{{/if}}
							{{ /items }}
						{{ else }}
							<div class="md:col-span-6">
								{{ trans:strings.no_results }}
							</div>
						{{ /unless }}
					</div>
				</div>
			</div>
		</div>
	{{ /entries }}
</div>
<!-- End: /perspective/index.antlers.html -->