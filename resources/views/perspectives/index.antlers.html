{{#
    @name Perspectives Index
#}}

<!-- /perspective/index.antlers.html -->
{{ if use_dark_header_theme }}
	{{ partial:layout/header theme="dark" }}
{{ else }}
	{{ partial:layout/header theme="light" }}
{{ /if }}

<div class="mt-10">
	<section class="c-text-feature rounded-t-lg bg-white z-30 relative snap-start ">
		<div class="container px-4">
			<div class="grid grid-cols-12">
				<div class="col-span-1 text-right">
					<div class="c-label"></div>
				</div>
				<div class="col-span-11 lg:col-span-7">
					<h1 class="flex font-bold text-36 sm:text-48 xl:text-64">
						These are our<br />Perspectives.
					</h1>
				</div>
			</div>
			<div class="grid grid-cols-12 mb-12">
				<div class="col-start-2 col-span-10 2xl:col-start-2 2xl:col-span-5">
					<div class="text-24 font-semibold mt-7">
						<p class="mb-6">The Platinum Seed blog is where we write about the industry we love. Our views, our thoughts...</p>
						<p>Our Perspectives.</p>
					</div>
				</div>
			</div>
		</div>
	</section>

	{{ page_builder scope="block" }}
		{{ partial src="page_builder/{type}" }}
	{{ /page_builder }}
</div>

{{ section:index_content }}
	{{ collection:perspectives sort="date:desc" as="items" }}
		<div class="container px-4">
			<div class="grid grid-cols-12 pb-19">
				<div class="col-start-2 col-span-10">
					<div class="c-tag-filter flex flex-wrap gap-4 pb-5 border-b border-dark mb-9">
						<div>{{ partial:atoms/tag url="/perspectives/" label="All Featured" selected="true" }}</div>
						{{ taxonomy from="perspectives_categories" collection="perspectives" }}
							<div>{{ partial:atoms/tag url="/perspectives/perspectives-categories/{{slug}}/" :label="title" }}</div>
						{{ /taxonomy }}
					</div>
					<div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
						{{ unless no_results }}
							{{ items }}
								{{ if count == 1 || count == 7}}
									{{ partial:components/blog-card with_image="true" theme="dark" :image="feature_image" :tag="perspectives_categories:0:title" :title="title" :excerpt="excerpt" class="col-span-1 md:col-span-2" }}
								{{ else }}
									{{ partial:components/blog-card theme="dark" :image="feature_image" :tag="perspectives_categories:0:title" :title="title" :excerpt="excerpt" class="col-span-1 md:col-span-2" }}
								{{/if}}
							{{ /items }}
						{{ else }}
							<div class="md:col-span-6">
								{{ trans:strings.no_results }}
							</div>
						{{ /unless }}
					</div>
				</div>
			</div>
		</div>
	{{ /collection:perspectives }}
{{ /section:index_content }}


<!-- End: /perspective/index.antlers.html -->