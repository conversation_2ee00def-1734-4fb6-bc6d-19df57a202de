{{#
    @name Home
    @desc Home page template, excludes header
#}}



<!-- /home.antlers.html pt-9 -->
<div id="fullpage" class="js-fullpage">
	<!-- Desktop header -->
	<section class="section">
		<div class="sticky top-0 left-0">
			<div class="hidden lg:block js-swipe-reveal-hero js-no-custom-cursor relative h-screen overflow-hidden">
				<div class="content bg-dark h-screen relative z-10">
					{{ partial:layout/header theme="dark" class="top-0 left-0 right-0 z-20 absolute" }}
					<div class="container px-4 mx-auto h-screen flex items-center">
						<div class="grid grid-cols-12">
							<div class="col-start-2 col-span-10">
								<h2 class="text-hero font-bold text-grey-800 leading-[1]">It's not<br /><span class="ml-6">about you.</span></h2>
								<div class="text-hero-sub font-bold text-right opacity-0"><span class="text-grey-200">we focus on the customer</span> <span class="text-accent">/</span> <span class="text-dark">yours</span></div>
							</div>
						</div>
					</div>
				</div>
				<div class="clip-polygon absolute top-0 left-0 right-0 bottom-0 max-w-full z-20 bg-white transition-all duration-200 ease-linear overflow-hidden">
					{{ partial:layout/header theme="light" class="top-0 left-0 right-0 z-20 absolute" }}
					<div class="container px-4 mx-auto h-screen flex items-center">
						<div class="grid grid-cols-12">
							<div class="col-start-2 col-span-10 -ml-[27px]">
								<h1 class="text-hero font-bold text-dark leading-[1]">It's<br /><span class="ml-6">about <span class="text-accent">them.</span></span></h1>
								<div class="text-hero-sub font-bold text-right"><span class="text-grey-200">we focus on the customer</span> <span class="text-accent">/</span> <span class="text-dark">yours</span></div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!-- Mobile header -->
			<div class="lg:hidden">
				<section class="js-mobile-hero-fade relative h-screen overflow-hidden">
					<div class="transition-all duration-300 ease-linear">
						<div class="bg-dark h-screen relative z-10">
							{{ partial:layout/header theme="dark" class="top-0 left-0 right-0 z-20 " }}
							<div class="container px-4 mx-auto h-screen flex justify-center items-center">
								<div class="grid grid-cols-12 -mt-[90px]">
									<div class="col-start-2 col-span-10">
										<h2 class="text-hero font-bold text-grey-800">It's not<br /><span class="ml-6">about you.</span></h2>
										<div class="text-hero-sub font-bold text-right opacity-0"><span class="text-grey-200">we focus on the customer</span> <span class="text-accent">/</span> <span class="text-dark">yours</span></div>
									</div>
								</div>
							</div>
						</div>
						<div class="js-mobile-hero-fade-target h-screen bg-white absolute top-0 left-0 right-0 opacity-0 transition-all duration-300 ease-linear">
							{{ partial:layout/header theme="light" class="top-0 left-0 right-0 z-20" }}
							<div class="container px-4 mx-auto h-screen flex justify-center items-center">
								<div class="grid grid-cols-12 -mt-[90px]">
									<div class="col-start-2 col-span-10">
										<h1 class="text-hero font-bold text-dark">It's<br /><span class="ml-6">about <span class="text-accent">them.</span></span></h1>
										<div class="text-hero-sub font-bold text-right"><span class="text-grey-200">we focus on the customer</span> <span class="text-accent">/</span> <span class="text-dark">yours</span></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</section>
			</div>
		</div>

		<div class="js-home-intro bg-white rounded-t-lg relative h-screen z-20 shadow-[0px_0px_1px_rgba(12,26,75,0.08),0px_-20px_16px_rgba(20,37,63,0.03)] flex flex-col justify-center">
			<div class="container px-4 py-11 sm:px-0 h-full">
				<div class="flex flex-col justify-between h-full">
					{{ partial:typography/label maxwidth="lg:col-span-7" }}
						Results-driven digital marketing tailored to your brand.
					{{ /partial:typography/label }}
					<div class="grid grid-cols-12 lg:mt-12">
						<div class="col-start-4 col-span-8 lg:col-start-9 lg:col-span-4">
							<svg width="274" height="40" viewBox="0 0 274 40" fill="none" xmlns="https://www.w3.org/2000/svg">
								<path d="M137.692 6.80078L132.941 28.612H137.902L142.653 6.80078L137.692 6.80078Z" fill="#FF0025"/>
								<path d="M1.08293 14.9467C1.81069 13.605 2.81208 12.5685 4.09875 11.8372C5.38543 11.1059 6.85839 10.7432 8.51768 10.7432C10.6253 10.7432 12.3661 11.2384 13.7459 12.2288C15.1257 13.2192 16.0573 14.63 16.5405 16.4611L12.6572 16.4611C12.3428 15.6089 11.8363 14.9409 11.1434 14.463C10.4506 13.985 9.5773 13.7432 8.5235 13.7432C7.05052 13.7432 5.87447 14.2557 5.00117 15.2864C4.12786 16.3171 3.6912 17.7567 3.6912 19.6108C3.6912 21.465 4.12786 22.9103 5.00117 23.9525C5.87447 24.9947 7.05052 25.513 8.5235 25.513C10.6078 25.513 11.9877 24.6089 12.6572 22.7951H16.5405C16.034 24.5456 15.0908 25.9333 13.6993 26.964C12.3079 27.9948 10.5845 28.5072 8.5235 28.5072C6.8584 28.5072 5.38543 28.1387 4.10458 27.3959C2.82373 26.6589 1.81648 25.6166 1.08872 24.275C0.360965 22.9333 0 21.3786 0 19.605C0 17.8315 0.360965 16.2768 1.08872 14.9351L1.08293 14.9467Z" fill="#16151A"/>
								<path d="M25.1979 11.4793C26.0712 10.9899 27.1075 10.748 28.3069 10.748V14.4333H27.3928C25.9839 14.4333 24.9126 14.7903 24.1907 15.4928C23.4629 16.2011 23.1019 17.4276 23.1019 19.1781L23.1019 28.2357H19.5039L19.5039 11.0302H23.1019V13.5293C23.6259 12.654 24.3304 11.9745 25.2037 11.4851L25.1979 11.4793Z" fill="#16151A"/>
								<path d="M46.809 20.9572L33.5172 20.9572C33.622 22.3334 34.1402 23.4332 35.0659 24.2681C35.9916 25.1031 37.1269 25.5177 38.4776 25.5177C40.4163 25.5177 41.7845 24.7173 42.5821 23.1107L46.4654 23.1107C45.9415 24.6942 44.9866 25.9898 43.6068 26.9975C42.227 28.0052 40.5153 28.5119 38.4776 28.5119C36.8125 28.5119 35.3279 28.1434 34.0121 27.4006C32.6963 26.6635 31.6658 25.6213 30.9206 24.2796C30.1753 22.938 29.7969 21.3833 29.7969 19.6097C29.7969 17.8362 30.1579 16.2815 30.8856 14.9398C31.6134 13.5982 32.6322 12.5617 33.948 11.8304C35.2638 11.0991 36.7717 10.7363 38.4776 10.7363C40.1835 10.7363 41.5807 11.0876 42.8674 11.7958C44.1483 12.5041 45.1497 13.5003 45.8658 14.7786C46.5819 16.0569 46.937 17.531 46.937 19.1951C46.937 19.8401 46.8963 20.4216 46.809 20.9456V20.9572ZM43.176 18.0838C43.1527 16.7709 42.6811 15.7229 41.7554 14.9283C40.8297 14.1394 39.6827 13.7421 38.3146 13.7421C37.0745 13.7421 36.009 14.1337 35.1241 14.911C34.2391 15.6941 33.7151 16.7479 33.5463 18.078H43.176V18.0838Z" fill="#16151A"/>
								<path d="M50.9891 14.9639C51.7169 13.6338 52.7066 12.5973 53.9584 11.8545C55.2101 11.1117 56.5958 10.7432 58.1095 10.7432C59.4777 10.7432 60.6712 11.008 61.6959 11.5378C62.7148 12.0676 63.5299 12.7298 64.1412 13.5186V11.0196L67.7742 11.0196V28.2251H64.1412V25.6627C63.5299 26.4746 62.6973 27.1541 61.6493 27.6953C60.5955 28.2366 59.3962 28.5072 58.0513 28.5072C56.555 28.5072 55.1869 28.1272 53.9468 27.3671C52.7067 26.607 51.7227 25.5475 50.995 24.1828C50.2672 22.8181 49.9062 21.2749 49.9062 19.5475C49.9062 17.82 50.2672 16.2883 50.995 14.9582L50.9891 14.9639ZM63.396 16.5244C62.9011 15.6492 62.2548 14.987 61.4572 14.5263C60.6596 14.0714 59.7921 13.8411 58.8664 13.8411C57.9407 13.8411 57.079 14.0657 56.2756 14.5148C55.478 14.9639 54.8259 15.6204 54.3368 16.4841C53.842 17.3478 53.5974 18.3728 53.5974 19.559C53.5974 20.7452 53.842 21.7874 54.3368 22.6799C54.8317 23.5725 55.4838 24.2577 56.293 24.7241C57.1023 25.1905 57.964 25.4266 58.8664 25.4266C59.7688 25.4266 60.6538 25.1963 61.4572 24.7414C62.2548 24.2865 62.9069 23.6128 63.396 22.726C63.8908 21.8392 64.1354 20.8085 64.1354 19.6166C64.1354 18.4246 63.885 17.3997 63.396 16.5244Z" fill="#16151A"/>
								<path d="M76.6999 13.9334V23.4575C76.6999 24.1024 76.8513 24.5631 77.1598 24.8452C77.4684 25.1274 77.9866 25.2656 78.7202 25.2656H80.9326V28.2311H78.0914C76.4729 28.2311 75.227 27.8568 74.3653 27.1082C73.5036 26.3596 73.0728 25.1389 73.0728 23.4575V13.9334H71.0234V11.0313H73.0728V6.75293L76.7058 6.75293V11.0313L80.9384 11.0313V13.9334L76.7058 13.9334H76.6999Z" fill="#16151A"/>
								<path d="M85.0661 8.09352C84.6236 7.65589 84.4023 7.11462 84.4023 6.4697C84.4023 5.82478 84.6236 5.28351 85.0661 4.84589C85.5085 4.40826 86.0558 4.18945 86.7079 4.18945C87.3599 4.18945 87.8781 4.40826 88.3206 4.84589C88.7631 5.28351 88.9843 5.82478 88.9843 6.4697C88.9843 7.11462 88.7631 7.65589 88.3206 8.09352C87.8781 8.53114 87.3425 8.74995 86.7079 8.74995C86.0733 8.74995 85.5085 8.53114 85.0661 8.09352ZM88.4778 11.0302V28.2357H84.8797V11.0302H88.4778Z" fill="#16151A"/>
								<path d="M99.9321 25.0525L104.857 11.0312L108.677 11.0312L102.045 28.2368H97.7488L91.1523 11.0312H95.0065L99.9321 25.0525Z" fill="#16151A"/>
								<path d="M126.578 20.9572L113.287 20.9572C113.392 22.3334 113.91 23.4332 114.835 24.2681C115.761 25.1031 116.896 25.5177 118.247 25.5177C120.186 25.5177 121.554 24.7173 122.352 23.1107L126.235 23.1107C125.711 24.6942 124.756 25.9898 123.376 26.9975C121.996 28.0052 120.285 28.5119 118.247 28.5119C116.582 28.5119 115.097 28.1434 113.782 27.4006C112.466 26.6635 111.435 25.6213 110.69 24.2796C109.945 22.938 109.566 21.3833 109.566 19.6097C109.566 17.8362 109.927 16.2815 110.655 14.9398C111.383 13.5982 112.402 12.5617 113.718 11.8304C115.033 11.0991 116.541 10.7363 118.247 10.7363C119.953 10.7363 121.35 11.0876 122.637 11.7958C123.918 12.5041 124.919 13.5003 125.635 14.7786C126.351 16.0569 126.707 17.531 126.707 19.1951C126.707 19.8401 126.666 20.4216 126.578 20.9456V20.9572ZM122.945 18.0838C122.922 16.7709 122.451 15.7229 121.525 14.9283C120.599 14.1394 119.452 13.7421 118.084 13.7421C116.844 13.7421 115.779 14.1337 114.894 14.911C114.009 15.6941 113.485 16.7479 113.316 18.078H122.945V18.0838Z" fill="#16151A"/>
								<path d="M156.524 13.4764V23.0005C156.524 23.6454 156.676 24.106 156.984 24.3882C157.293 24.6703 157.811 24.8085 158.544 24.8085H160.757V27.774H157.916C156.297 27.774 155.051 27.3997 154.19 26.6512C153.328 25.9026 152.897 24.6819 152.897 23.0005V13.4764H150.848V10.5743H152.897V6.2959L156.53 6.2959V10.5743L160.763 10.5743V13.4764L156.53 13.4764H156.524Z" fill="#16151A"/>
								<path d="M176.617 11.1325C177.659 11.6968 178.48 12.526 179.08 13.6315C179.679 14.7371 179.982 16.0673 179.982 17.6277V27.7737H176.413V18.1575C176.413 16.6201 176.023 15.4339 175.243 14.6162C174.463 13.7928 173.403 13.3839 172.052 13.3839C170.702 13.3839 169.636 13.7928 168.85 14.6162C168.058 15.4396 167.668 16.6201 167.668 18.1575V27.7737H164.07V4.66602L167.668 4.66602V12.5663C168.28 11.835 169.054 11.2764 169.991 10.8791C170.929 10.4818 171.953 10.286 173.071 10.286C174.399 10.286 175.581 10.5682 176.623 11.1267L176.617 11.1325Z" fill="#16151A"/>
								<path d="M184.113 7.63746C183.67 7.19984 183.449 6.65857 183.449 6.01365C183.449 5.36873 183.67 4.82746 184.113 4.38983C184.555 3.95221 185.103 3.7334 185.755 3.7334C186.407 3.7334 186.925 3.95221 187.367 4.38983C187.81 4.82746 188.031 5.36873 188.031 6.01365C188.031 6.65857 187.81 7.19984 187.367 7.63746C186.925 8.07509 186.389 8.2939 185.755 8.2939C185.12 8.2939 184.555 8.07509 184.113 7.63746ZM187.525 10.5742V27.7797H183.927V10.5742H187.525Z" fill="#16151A"/>
								<path d="M204.082 11.1327C205.165 11.697 206.015 12.5262 206.627 13.6318C207.238 14.7373 207.541 16.0675 207.541 17.628V27.7739H203.972V18.1577C203.972 16.6203 203.582 15.4341 202.801 14.6164C202.021 13.793 200.962 13.3842 199.611 13.3842C198.26 13.3842 197.195 13.793 196.409 14.6164C195.617 15.4398 195.227 16.6203 195.227 18.1577V27.7739H191.629V10.5684H195.227V12.5377C195.815 11.8294 196.566 11.2766 197.486 10.8851C198.4 10.4878 199.372 10.292 200.409 10.292C201.777 10.292 203.005 10.5741 204.088 11.1327H204.082Z" fill="#16151A"/>
								<path d="M218.366 19.1882L226.383 27.7737L221.522 27.7737L215.082 20.3744V27.7737H211.484V4.66602L215.082 4.66602V18.0942L221.399 10.5682L226.389 10.5682L218.372 19.1882H218.366Z" fill="#16151A"/>
								<path d="M229.797 7.63746C229.354 7.19984 229.133 6.65857 229.133 6.01365C229.133 5.36873 229.354 4.82746 229.797 4.38983C230.239 3.95221 230.786 3.7334 231.438 3.7334C232.09 3.7334 232.609 3.95221 233.051 4.38983C233.494 4.82746 233.715 5.36873 233.715 6.01365C233.715 6.65857 233.494 7.19984 233.051 7.63746C232.609 8.07509 232.073 8.2939 231.438 8.2939C230.804 8.2939 230.239 8.07509 229.797 7.63746ZM233.208 10.5742V27.7797H229.61V10.5742H233.208Z" fill="#16151A"/>
								<path d="M249.758 11.1327C250.841 11.697 251.691 12.5262 252.302 13.6318C252.914 14.7373 253.216 16.0675 253.216 17.628V27.7739H249.647V18.1577C249.647 16.6203 249.257 15.4341 248.477 14.6164C247.697 13.793 246.637 13.3842 245.287 13.3842C243.936 13.3842 242.871 13.793 242.085 14.6164C241.293 15.4398 240.903 16.6203 240.903 18.1577V27.7739H237.305V10.5684H240.903V12.5377C241.491 11.8294 242.242 11.2766 243.162 10.8851C244.076 10.4878 245.048 10.292 246.084 10.292C247.453 10.292 248.681 10.5741 249.764 11.1327H249.758Z" fill="#16151A"/>
								<path d="M267.754 11.0864C268.797 11.6162 269.612 12.2784 270.2 13.0672V10.5682L273.833 10.5682V28.0559C273.833 29.6394 273.495 31.0501 272.82 32.2881C272.144 33.5262 271.172 34.4993 269.897 35.2076C268.622 35.9158 267.102 36.2671 265.332 36.2671C262.975 36.2671 261.018 35.72 259.458 34.626C257.898 33.5319 257.019 32.0521 256.809 30.1749H260.378C260.652 31.0674 261.234 31.7929 262.13 32.3457C263.027 32.8985 264.092 33.1749 265.332 33.1749C266.782 33.1749 267.958 32.7373 268.855 31.862C269.751 30.9868 270.194 29.72 270.194 28.0501V25.1768C269.583 25.9887 268.756 26.6681 267.714 27.2209C266.672 27.7737 265.49 28.0501 264.162 28.0501C262.648 28.0501 261.263 27.6701 260.011 26.91C258.759 26.1499 257.77 25.0904 257.042 23.7257C256.314 22.361 255.953 20.8178 255.953 19.0903C255.953 17.3629 256.314 15.8312 257.042 14.501C257.77 13.1709 258.759 12.1344 260.011 11.3916C261.263 10.6546 262.648 10.2803 264.162 10.2803C265.507 10.2803 266.701 10.5451 267.749 11.0749L267.754 11.0864ZM269.46 16.0673C268.965 15.192 268.319 14.5298 267.522 14.0692C266.724 13.6143 265.856 13.3839 264.931 13.3839C264.005 13.3839 263.143 13.6085 262.34 14.0577C261.542 14.5068 260.89 15.1632 260.401 16.027C259.906 16.8907 259.662 17.9157 259.662 19.1018C259.662 20.288 259.906 21.3303 260.401 22.2228C260.896 23.1153 261.548 23.8005 262.357 24.267C263.167 24.7334 264.028 24.9695 264.931 24.9695C265.833 24.9695 266.718 24.7391 267.522 24.2842C268.319 23.8293 268.971 23.1556 269.46 22.2689C269.955 21.3821 270.2 20.3514 270.2 19.1594C270.2 17.9675 269.949 16.9425 269.46 16.0673Z" fill="#16151A"/>
							</svg>
						</div>
						<div class="col-start-1 lg:col-start-2 col-span-10 h-[1px] bg-dark my-2"></div>
						<div class="col-start-4 col-span-8 lg:col-start-9 lg:col-span-3">
							<p class="text-18">Our approach to helping businesses build better websites and develop their brand’s identity through engaging social media and compelling online experiences.</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	</section>

	<section class="section mb-12 md:mb-0">
		<div class="js-our-work-slider swiper swiper-container h-screen">
			<div class="swiper-wrapper transition-all duration-300 ease-linear">
				<div class="swiper-slide py-8 slide1 js-swiper-slide-0 w-screen min-h-full md:rounded-l-lg bg-white">
					<div class="container pl-7 pr-0 sm:px-0 md:pl-7 mx-auto mr-0 h-screen md:h-full flex justify-start items-center">
						<div class="w-full flex flex-col justify-between">
							<div class="mb-4 md:mb-10">
								<div class="js-slide-text transition-all duration-300 xl:px-0">
									<div class="text-18 sm:text-24 font-semibold mb-3">Platinum Seed</div>
									<div class="text-32 sm:text-48 xl:text-64 font-bold">Some of our work:<br /> <span class="opacity-0">One build at a time.</div>
								</div>
							</div>
							<div class="shrink opacity-0">
								{{ responsive:featured_case_studies:home_slider_case_studies:1:feature_image class="w-full" }}
							</div>
						</div>
					</div>
				</div>
				{{ featured_case_studies:home_slider_case_studies }}
					{{?
						switch ($colour_scheme) {
							case 'iclei':
								$bg = 'bg-iclei-bold';
								$cursor_colour = 'bg-iclei-accent';
								break;
							case 'cwn':
								$bg = 'bg-cwn-bold';
								$cursor_colour = 'bg-cwn-accent';
								break;
							case 'joshu':
								$bg = 'bg-joshu-bold';
								$cursor_colour = 'bg-joshu-accent';
								break;
							case 'dineplan':
								$bg = 'bg-dineplan-bold';
								$cursor_colour = 'bg-dineplan-accent';
								break;
							case 'skinrenewal':
								$bg = 'bg-skinrenewal-bold';
								$cursor_colour = 'bg-skinrenewal-accent';
								break;
							case 'brightplan':
								$bg = 'bg-brightplan-bold';
								$cursor_colour = 'bg-brightplan-accent';
								break;
							case 'nac':
								$bg = 'bg-nac-bold';
								$cursor_colour = 'bg-nac-accent';
								break;
							case 'skinrenewal_historical':
								$bg = 'bg-skinrenewal_historical-bold';
								$cursor_colour = 'bg-skinrenewal_historical-accent';
								break;
							case 'farosian':
								$bg = 'bg-farosian-bold';
								$cursor_colour = 'bg-farosian-accent';
								break;
							case 'okumhlophe':
								$bg = 'bg-okumhlophe-bold';
								$cursor_colour = 'bg-okumhlophe-accent';
								break;
							case 'valenture':
								$bg = 'bg-valenture-bold';
								$cursor_colour = 'bg-valenture-accent';
								break;
							case 'pmi':
								$bg = 'bg-pmi-bold';
								$cursor_colour = 'bg-pmi-accent';
								break;
							case 'ramp':
								$bg = 'bg-ramp-bold';
								$cursor_colour = 'bg-ramp-accent';
								break;
							case 'duram':
								$bg = 'bg-duram-bold';
								$cursor_colour = 'bg-duram-accent';
								break;
							default:
								$bg = 'bg-dark';
								$cursor_colour = 'bg-accent';
						};
					?}}
					<a href="{{ url | ensure_right('/') }}" class="swiper-slide js-swiper-slide-{{ index + 1 }} block py-8 md:rounded-l-lg {{ bg }} js-view-case-cursor {{ if last }} js-last-slide {{ /if }}" data-bg="{{ cursor_colour }}">
						<div class="container pl-7 pr-0 sm:px-0 md:pl-7 mx-auto mr-0 h-screen md:h-full flex justify-start items-center">
							<div class="w-full flex flex-col justify-between">
								<div class="mb-4 md:mb-10">
									<div class="js-slide-text js-slide-text-1 text-white transition-all duration-300">
										<div class="text-18 sm:text-24 font-semibold mb-3">{{ title }}</div>
										<div class="text-32 sm:text-48 xl:text-64 font-bold">{{ hero_title }}</div>
										{{ partial:components/button as="span" theme="dark" class="xl:hidden mt-3 md:mt-6" type="line" label="VIEW CASE" }}
									</div>
								</div>
								<div class="shrink">
									{{ responsive:feature_image class="w-full" }}
								</div>
							</div>
						</div>
					</a>
				{{ /featured_case_studies:home_slider_case_studies }}
			</div>
		</div>
	</section>

	{{ page_builder scope="block" }} 
		{{ partial src="page_builder/accordion" }}
	{{ /page_builder }}

	<section class="section rounded-t-lg bg-white">
		<div class="bg-white md:h-screen relative z-20 flex flex-col justify-center mb-12">
			<div class="container px-4 py-13 md:py-0">
				<div class="grid grid-cols-12">
					<div class="col-span-12 2xl:col-start-2 2xl:col-span-7">
						<div class="text-md:24 font-semibold mb-3">Why do we do it?</div>
					</div>
				</div>
				{{ partial:typography/label maxwidth="lg:col-span-10 2xl::col-span-8" }}
					The chance to get strategically imaginative with valuable ideas and turn them into real business growth.
				{{ /partial:typography/label }}
			</div>
		</div>

		{{ partial:layout/footer }}
	</section>
</div>
<!-- End: /home.antlers.html -->
