{{#
    @name Services show
    @desc The Services show template.
#}}

{{?
	$breadcrumbs = [
		[
			'title' => 'Our Services',
			'breadcrumb_url' => '/services'
		],
		[
			'title' => $title,
			'breadcrumb_url' => "#"
		]
	]
?}}

<div id="fullpage" class="js-homepage">
	<section class="section">
		<div
			class="bg-primary bg-center bg-no-repeat bg-cover relative">
			<div class="absolute top-0 left-0 w-full z-40">{{ partial:layout/header breadcrumbs="{{breadcrumbs}}" }}</div>
			<div class="container px-4 pt-14 pb-19 relative z-30">
				<div class="grid grid-cols-12 mb-11 lg:mb-17">
					<div class="col-span-2 sm:col-span-1 text-right">
						<div class="
							inline-block
							w-4
							h-[90px]
							sm:w-5
							sm:h-13
							lg:w-7
							lg:h-[220px]
							border-[2px]
							sm:border-4
							lg:border-[5px]
							border-white
							-skew-x-[15deg]
							translate-y-3
							lg:translate-y-5
							mr-6
							sm:mr-10
						"></div>
					</div>
					<div class="col-span-10 sm:col-span-11 2xl:col-span-8">
						<h1 class="text-white text-48 sm:text-56 lg:text-112 font-bold mb-8">{{ title }}</h1>
						{{ if header_description }}
							<div
								class="
									text-white
									font-semibold
									prose
									prose-p:text-24
									prose-p:text-white
									max-w-none
								"
							>
								{{ header_description }}
							</div>
						{{ /if }}
					</div>
				</div>
				<div class="text-center text-white mb-10 lg:mb-11">
					<div class="text-48 lg:text-64 font-bold mb-3">It's our thinking that makes us.</div>
					<div class="text-24 font-semibold">{{ detail_caption_line_1 }}</div>
					<div class="text-24 font-semibold">{{ detail_caption_line_2 }}</div>
				</div>
				<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-10">
					{{ our_thinking_cards }}
							<div class="rounded-lg p-7 bg-black">
								<div class="flex flex-col gap-7">
									<div>
										{{ tag }}
											<div class="text-32 font-bold text-accent mb-3">{{ title }}</div>
										{{ /tag }}
										<div class="text-24 font-semibold text-white">{{ title }}</div>
									</div>
									{{ tag }}
										<a href="/our-work/tags/{{slug}}/" class="w-[72px] h-[72px] bg-[#1F1F1F] hover:bg-accent transition-all duration-300 ease-linear rounded-full flex flex-col justify-center text-center">
											<svg class="block mx-auto" width="44" height="44" viewBox="0 0 44 44" fill="none" xmlns="http://www.w3.org/2000/svg">
												<path d="M16.5 33L27.5 22L16.5 11" stroke="#E8E8E8" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
											</svg>
										</a>
									{{ /tag }}
								</div>
							</div>
					{{ /our_thinking_cards }}
				</div>
			</div>
		</div>
	</section>

	<section class="section mb-12 md:mb-0 bg-primary">
		<div class="js-our-work-slider swiper swiper-container h-screen">
			<div class="swiper-wrapper transition-all duration-300 ease-linear">
				{{ featured_case_studies }}
					<a href="{{ url | ensure_right('/') }}" class="swiper-slide js-swiper-slide-{{ index }} block py-8 md:rounded-l-lg bg-{{ colour_scheme }}-bold js-view-case-cursor {{ if last }} js-last-slide {{ /if }}" data-bg="bg-{{ colour_scheme }}-accent">
						<div class="container pl-7 pr-0 sm:px-0 md:pl-7 mx-auto mr-0 h-screen md:h-full flex justify-start items-center">
							<div class="w-full flex flex-col justify-between">
								<div class="mb-4 md:mb-10">
									<div class="js-slide-text js-slide-text-1 text-white transition-all duration-300">
										<div class="text-18 sm:text-24 font-semibold mb-3">{{ title }}</div>
										<div class="text-32 sm:text-48 xl:text-64 font-bold">{{ hero_title }}</div>
										{{ partial:components/button as="span" theme="dark" class="xl:hidden mt-3 md:mt-6" type="line" label="VIEW CASE" }}
									</div>
								</div>
								<div class="shrink">
									{{ responsive:feature_image class="w-full" :alt="title" }}
								</div>
							</div>
						</div>
					</a>
				{{ /featured_case_studies }}
			</div>
		</div>
	</section>

	<section class="section">
		<div class="bg-white py-12 lg:py-17 rounded-t-lg shadow-[0px_0px_1px_rgba(12,26,75,0.08),0px_-20px_16px_rgba(20,37,63,0.03)] relative z-10">
			<div class="grid place-content-center h-full">
				<div class="container px-4 mb-12">
					{{ partial:typography/label maxwidth="lg:col-span-8" }}
						Our clients are<br />
						our partners.
					{{ /partial:typography/label }}
				</div>
				{{ image_marqee_rows }}
					{{ partial:components/image_marquee }}
				{{ /image_marqee_rows }}
			</div>
		</div> 

		{{ if (faqs) }}
			<div class="bg-white py-12 lg:py-17 -mt-7 rounded-t-lg shadow-[0px_-20px_16px_0px_rgba(20,_37,_63,_0.03),_0px_0px_1px_0px_rgba(12,_26,_75,_0.08)] relative z-10">
				<div class="container px-4">
					<div x-data="{ showMore: false }" class="grid grid-cols-12">
						<div class="col-start-2 col-span-10 lg:col-start-2 lg:col-span-6">
							<div class="text-24 font-semibold">An FAQ or two</div>
						</div>
						<div class="col-start-1 col-span-1 text-right">
							<div class="c-label"></div>
						</div>
						<div class="col-start-2 col-span-10 lg:col-span-6 mb-6">
							<div class="font-bold text-48 xl:text-64 mb-3">Frequently<br />Asked Questions.</div>
						</div>
						<div class="col-start-2 col-span-10 grid grid-cols-1 lg:grid-cols-3 gap-6 mt-10">
							{{ faqs }}
								<div {{ if count > 3}}x-show="showMore"{{/if}} class="border-t border-grey-900 pt-6">
									<div class="text-24 font-bold mb-3">{{ question }}</div>
									<p>{{ answer }}</p>
								</div>
							{{ /faqs }}
							
						</div>
						{{ if (faqs | length) > 3 }}
							<div class="col-span-12 text-center mt-10">
								{{ partial:components/button as="button" type="filled" label="LOAD MORE" custom_attributes='x-show="!showMore" @click="showMore = true"' class="''"}}
								{{ partial:components/button as="button" type="filled" label="LOAD LESS" custom_attributes='x-show="showMore" @click="showMore = false"' class="''"}}
							</div>
						{{ /if }}
					</div>
				</div>
			</div>
		{{ /if }}

		{{ partial:layout/footer }}
	</section>
</div>