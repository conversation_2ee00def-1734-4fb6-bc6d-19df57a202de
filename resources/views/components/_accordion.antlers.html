<div x-data="{selected:1, showMore: false}">
	<ul class="border-t border-gray-900">
		{{ items }}
			<li {{ if count > 4}}x-show="showMore"{{/if}} class="relative border-b border-gray-900">

				<div class="w-full py-5">
					<div class="grid grid-cols-12 gap-4 items-center">
						<div class="col-span-12 sm:col-span-8 xl:col-span-2 font-semibold">{{ client_brand }}</div>
						<div class="col-span-3 hidden xl:block" :class="selected == {{count}} ? 'font-semibold' : ''">{{ client_name }}</div>
						<div class="col-span-3 hidden xl:block" :class="selected == {{count}} ? 'font-semibold' : ''">{{ industry }}</div>
						<div class="col-span-2 hidden xl:block" :class="selected == {{count}} ? 'font-semibold' : ''">{{ service_description }}</div>
						<div class="col-span-12 sm:col-span-4 xl:col-span-2 sm:text-right">
							<div x-show="selected != {{count}}">{{ partial:components/button type="line" label="Read more" custom_attributes="@click.prevent='selected !== {{count}} ? selected = {{count}} : selected = null'"}}</div>
							<div x-show="selected == {{count}}">{{ partial:components/button type="line" label="Close" right_icon="link" custom_attributes="@click.prevent='selected !== {{count}} ? selected = {{count}} : selected = null'"}}</div>
						</div>
					</div>
				</div>

				<div class="" x-show="selected == {{ count }}" x-collapse>
					<div class="grid grid-cols-12 auto-rows-auto gap-4 mb-6">
						<div class="col-span-12 xl:col-span-2"></div>
						<div class="col-span-12 xl:col-span-3">"{{ quote }}"</div>
						<div class="col-span-12 xl:col-span-3"></div>
						<div class="col-span-12 xl:col-span-3">
							<div class="flex gap-3 flex-wrap">
								{{ services }}
									<div class="grow-0 shrink">{{ partial:atoms/tag as="div" theme="dark" :label="value" classes="" }}</div>
								{{ /services }}
							</div>
						</div>
						<div class="col-span-12 xl:col-span-1"></div>
					</div>
					<div class="grid grid-cols-12 gap-6 mb-6">
						{{ images }}
							<div class="col-span-12 
							{{ switch(
								(width == '2') => 'xl:col-span-4',
								(width == '4') => 'xl:col-span-3',
								(width == '6') => 'xl:col-span-5',
								() => '12'
							)}}">
								{{ responsive:responsive_image class="rounded-t-md w-full h-full object-cover" :alt="client_name + ' ' + client_brand + ' - ' + count" }}
							</div>
						{{ /images }}
					</div>
				</div>

			</li>
		{{ /items }}
	</ul>
	{{ if (items | length) > 4 }}
		<div class="col-span-12 text-center mt-10">
			{{ partial:components/button as="button" type="filled" label="LOAD MORE" custom_attributes='x-show="!showMore" @click="showMore = true"' class="''"}}
			{{ partial:components/button as="button" type="filled" label="LOAD LESS" custom_attributes='x-show="showMore" @click="showMore = false"' class="''"}}
		</div>
	{{ /if }}
</div>