{{if with_image }}
	{{ partial:snippets/card as="a" :card_url="url" :theme="theme" cardbg="bg-grey-050" classes="shadow-md p-6 {{ class }}" }}
		<div class="lg:grid lg:grid-cols-2 gap-6 h-full">
			<div class="mb-6 lg:mb-0">
				{{ responsive:image ratio="1.15" class="h-full w-full object-cover rounded-md" :alt="title" }}
			</div>
			<div class="flex flex-col justify-between lg:h-full">
				<div class="flex justify-between mb-6">
					<div>
						<span class="
							inline-block 
							rounded-sm 
							hover:font-semibold 
							tracking-[.01em]
							hover:tracking-normal
							px-4 
							py-3
							bg-white
							text-dark
							capitalize
						">
							{{ tag }}
						</span>
					</div>
					<div>{{ partial:components/button theme="light" as="span" type="line" left_icon="link" }}</div>
				</div>
				<div>
					<div class="font-bold text-36 mb-3 line-clamp-5">
						{{ title }}
					</div>
					<p class="text-grey-800 line-clamp-3">
						{{ excerpt }}
					</p>
				</div>
			</div>
		</div>
	{{ /partial:snippets/card }}
{{/else}}
	{{ partial:snippets/card as="a" :card_url="url" :theme="theme" :cardbg="blog-card-bg" classes="shadow-md h-[446px] p-6 block" }}
		<div class="flex flex-col justify-between h-full">
			<div class="flex justify-between">
				<div>{{ partial:atoms/tag theme="{{theme}}" as="span" :label="tag" }}</div>
				<div>{{ partial:components/button theme="light" as="span" type="line" left_icon="link" }}</div>
			</div>
			<div>
				<div class="font-bold text-36 mb-3 line-clamp-5">
					{{title}}
				</div>
				<p class="text-grey-800 line-clamp-3">
					{{excerpt}}
				</p>
			</div>
		</div>
	{{ /partial:snippets/card }}
{{/if}}