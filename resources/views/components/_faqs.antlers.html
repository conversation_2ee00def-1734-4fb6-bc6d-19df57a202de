<section class="py-12 lg:py-17 {{class}}">
	<div class="container px-4">
		<div x-data="{ showMore: false }" class="grid grid-cols-12">
			<div class="col-start-2 col-span-10 lg:col-start-2 lg:col-span-6">
				<div class="text-24 font-semibold">An FAQ or two</div>
			</div>
			<div class="col-start-1 col-span-1 text-right">
				<div class="c-label"></div>
			</div>
			<div class="col-start-2 col-span-10 lg:col-span-6 mb-6">
				<div class="font-bold text-48 xl:text-64 mb-3">Frequently<br />Asked Questions.</div>
			</div>
			<div class="col-start-2 col-span-10 grid grid-cols-1 lg:grid-cols-3 gap-6 mt-10">
				{{ frequently_asked_questions:faqs }}
					<div {{ if count > 3}}x-show="showMore"{{/if}} class="border-t border-grey-900 pt-6">
						<div class="text-24 font-bold mb-3">{{ question }}</div>
						<p>{{ answer }}</p>
					</div>
				{{ /frequently_asked_questions:faqs }}
				
			</div>
			{{ if (frequently_asked_questions:faqs | length) > 3 }}
				<div class="col-span-12 text-center mt-10">
					{{ partial:components/button as="button" type="filled" label="LOAD MORE" custom_attributes='x-show="!showMore" @click="showMore = true"' class="''"}}
					{{ partial:components/button as="button" type="filled" label="LOAD LESS" custom_attributes='x-show="showMore" @click="showMore = false"' class="''"}}
				</div>
			{{ /if }}
		</div>
	</div>
</section>