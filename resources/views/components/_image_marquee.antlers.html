{{#
    @name Image marquee
    @desc The Image marquee page builder block.
    @set page.page_builder.image_marquee
#}}

{{?
	$count = 11;
	$myArray = $marquee_images->raw();
	$filled_marquee_images = [];

	// Loop
	for ($i = 0; $i < $count; $i++) {
		// Use the modulus operator to get the current item from the array
		$arrayIndex = $i % count($myArray);
		$filled_marquee_images[] = $myArray[$arrayIndex];
	}
?}}

<!-- /page_builder/_image_marquee.antlers.html -->
{{? $image_width = $image_width ?? 260 ?}}
{{? $image_height = $image_height ?? 260 ?}}
<section class="c-marquee mb-7">
	<div class="marquee relative {{ if reverse }}marquee--reverse{{ /if }} flex overflow-hidden select-none gap-4">
		<ul class="marquee__content shrink-0 flex justify-around gap-4 min-w-full">
			{{filled_marquee_images}}
				<li class="shrink-0">
					<img 
						src="{{ glide src="/images/{{ value }}" width="{{ image_width }}" height="{{ image_height }}" }}" 
						lazy="true"
						class="object-cover {{ image_classes }}" 
						style="width:{{ image_width }}px; height:{{ image_height }}px"
						alt="{{ value | replace('img', 'images') }}"
					/>
				</li>
			{{/filled_marquee_images}}
		</ul>
		<ul aria-hidden="true" class="marquee__content shrink-0 flex justify-around gap-4 min-w-full">
			{{filled_marquee_images}}
				<li>
					<img src="{{ glide src="/images/{{ value }}" width="{{ image_width }}" height="{{ image_height }}" }}" 
						lazy="true"
						class="object-cover {{ image_classes }}" 
						style="width:{{ image_width }}px; height:{{ image_height }}px"
						alt="{{ value | replace('img', 'images') }}"
					/>
				</li>
			{{/filled_marquee_images}}
		</ul>
	</div>
</section>
<!-- End: /page_builder/_image_marquee.antlers.html -->



