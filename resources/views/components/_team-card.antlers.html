<div class="c-team-card group !h-auto !flex flex-col justify-between {{class}}">
	<div>
		<div class="relative">
			{{ responsive:rollover class="rounded-t-sm object-cover z-10" alt="{{name}}"}}
			{{ responsive:image alt="{{name}} rollover" class="rounded-t-sm
				absolute
				top-0
				bottom-0
				left-0
				right-0
				h-full
				object-cover
				opacity-100
				group-hover:opacity-0
				transition-opacity
				duration-300"
			}}
		</div>
		<hr class="h-px mt-4 mb-6 border-grey-900 border-1" />
		<div class="font-bold text-24 mb-1">
			{{name}}
		</div>
	</div>
	<div class="flex items-end">
		<p class="text-grey-700">{{description}}</p>
		{{if linkedin}}
			<a target="_blank" class="ml-3 p-3 rounded-sm shadow-sm bg-white shrink-0" href="{{linkedin}}">
				{{partial:icons/linkedin class="''"}}
			</a>
		{{/if}}
	</div>
</div>