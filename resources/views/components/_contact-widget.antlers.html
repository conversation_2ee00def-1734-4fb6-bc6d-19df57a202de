<div 
	x-data="{ showQuickContact : false }" 
	:class="!showQuickContact ? 'hover:shadow-[none]' : ''"
	class="fixed max-sm:w-[calc(100%_-_40px)] bottom-5 right-5 sm:bottom-7 sm:right-8 z-50 rounded-[15px] bg-white shadow-card-tight transition-all duration-300 ease-in"
>
	<button 
		x-show="!showQuickContact" 
		@click="showQuickContact = !showQuickContact" 
		class="w-[100px] h-[70px] rounded-[15px] flex justify-center items-center text-[40px] font-bold hover:bg-accent transition-all duration-300"
	>
		/
	</button>
	<div x-cloak x-show="showQuickContact" class="py-8 px-5 relative">
		<button @click="showQuickContact = !showQuickContact" class="absolute top-4 right-5 text-dark group text-4xl font-bold cursor-pointer">
			<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
				<path class="stroke-dark group-hover:stroke-accent" d="M1.375 1.07227L20.875 20.5723" stroke-width="2.5"/>
				<path class="stroke-dark group-hover:stroke-accent" d="M20.875 1.07227L1.375 20.5723" stroke-width="2.5"/>
			</svg>				
		</button>
		{{ partial:components/quick_contact }}
	</div>
</div>