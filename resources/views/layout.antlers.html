{{#
    @name Layout
    @desc The default layout file.
#}}

<!-- /layout.antlers.html -->
<!doctype html>
<html lang="{{ site:short_locale }}" class="antialiased">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
		<meta name="mobile-web-app-capable" content="yes" />
        {{ vite src="resources/css/site.css|resources/js/site.js" }}
        {{ partial:snippets/seo }}
        {{ partial:snippets/browser_appearance }}
		<meta name="google-site-verification" content="8nuI7X6Xdo60hJoRoKzqTysp6gDekeh4QaSQzllVWFU" />
    </head>
    <body class="static min-h-screen bg-white selection:bg-primary selection:text-white {{if is_homepage}}home{{/if}}">
		<div class="
			js-cursor-dot
			bg-accent
			fixed
			top-0
			left-0
			w-2 h-2
			transition-all
			ease-out
			pointer-events-none
			rounded-full
			translate-z-0
			-translate-x-1
			-translate-y-1
			lg:grid
			place-items-center
			z-30
			hidden
		">
			<span class="hidden text-white text-14 font-semibold">VIEW<br />CASE</span>
		</div>
		{{ partial:navigation/off-canvas-navigation }}

		{{ yield:seo_body }}
        
		<main class="js-main bg-white relative z-10 transition-all duration-300">
			{{ template_content }}
		</main>

		{{ partial:components/contact-widget color="text-white" }}

		{{ partial:layout/footer }}

		<script type="text/javascript">
			_linkedin_partner_id = "4051634";
			window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
			window._linkedin_data_partner_ids.push(_linkedin_partner_id);
			</script><script type="text/javascript">
			(function(l) {
			if (!l){window.lintrk = function(a,b){window.lintrk.q.push([a,b])};
			window.lintrk.q=[]}
			var s = document.getElementsByTagName("script")[0];
			var b = document.createElement("script");
			b.type = "text/javascript";b.async = true;
			b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
			s.parentNode.insertBefore(b, s);})(window.lintrk);
		</script>
		<noscript>
			<img height="1" width="1" style="display:none;" alt="LinkedInPixel" src="https://px.ads.linkedin.com/collect/?pid=4051634&fmt=gif" />
		</noscript>
    </body>
</html>
<!-- End: /layout.antlers.html -->
