import Alpine from 'alpinejs'
import collapse from '@alpinejs/collapse'

//Components
// import revealHero from './components/reveal-hero'
import customCursor from './components/custom-cursor'
import homePageUi from './components/homepage-ui'
import teamSlider from './components/team-slider'
import mobileRelatedPerspectivesSlider from './components/mobile-related-perspectives-slider'
import servicesSlider from './components/services-slider'
import servicesReveals from './components/services-reveals'
// import pdfSlider from './components/pdf-slider'

// Global get CSRF token function (used by forms).
window.getToken = async () => {
    return await fetch('https://api.platinumseed.com/!/DynamicToken/refresh')
        .then((res) => res.json())
        .then((data) => {
            return data.csrf_token
        })
        .catch(function (error) {
            this.error = 'Something went wrong. Please try again later.'
        })
}

// Call Alpine.
window.Alpine = Alpine
Alpine.plugin(collapse)
Alpine.start()

//Init components
//Global
customCursor.init()

//Homepage
if (document.querySelector('.js-home-page')) {
	fullPage.init()
}

//Reveal Hero
// if (document.querySelector('.js-swipe-reveal-hero')) {
// 	revealHero.init()
// }

//Our Work Slider and Homepage fullpage scrolling
if (document.querySelector('.js-homepage')) {
	homePageUi.init()
}

//Team Slider
if (document.querySelector('.js-team-slider')) {
	teamSlider.init()
}

//Mobile related perspectives slider
if (document.querySelector('.js-mobile-related-perspectives-slider')) {
	mobileRelatedPerspectivesSlider.init()
}

//Services Slider
if (document.querySelector('.js-services-slider')) {
	servicesSlider.init()
}

//Services Slider
if (document.querySelector('.js-fade-in-up')) {
	servicesReveals.init()
}

//PDF slider
// if (document.querySelector('.js-pdf-slider')) {
// 	pdfSlider.init()
// }
