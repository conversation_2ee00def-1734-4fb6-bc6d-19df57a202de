// import Swiper, { Mousewheel } from 'swiper';
// import 'swiper/css';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

export default {
	init() {		
		// var swiper = new Swiper(".js-team-slider", {
		// 	modules: [Mousewheel],
		// 	mousewheel: {
		// 		releaseOnEdges: true
		// 	},
		// 	slidesPerView: 1.5,
		// 	grabCursor: true,
		// 	spaceBetween: 30,
		// 	freeMode: true,
		// 	centeredSlides: true,
		// 	breakpoints: {
		// 		1024: {
		// 			slidesPerView: 4,
		// 		},
		// 	}
		// });

		gsap.registerPlugin(ScrollTrigger)
		const services = document.querySelector(".js-team-slider")
		const lockAtPosition = services.dataset.lockposition ?? '10%';

		function getScrollAmount() {
			let servicesWidth = services.scrollWidth
			
			return -(servicesWidth - window.innerWidth + (window.innerWidth * 20 / 100))
		}

		const tween = gsap.to(services, {
			x: getScrollAmount,
			duration: 3,
			ease: "none",
		});

		ScrollTrigger.create({
			trigger: ".js-team-slider-wrapper",
			start: `top ${lockAtPosition}`,
			end: () => `+=${getScrollAmount() * -1}`,
			pin:true,
			animation: tween,
			scrub: 2,
			invalidateOnRefresh: true,
		})
	}
}