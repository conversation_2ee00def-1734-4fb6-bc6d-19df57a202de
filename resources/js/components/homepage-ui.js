import Swiper, { Mousewheel, EffectCreative, Navigation, Autoplay } from 'swiper';
import 'swiper/css';
import fullpage from 'fullpage.js/dist/fullpage.extensions.min';
import 'fullpage.js/dist/fullpage.min.css'
import mobileHeroFader from './mobile-hero-fader'
import mobileDetect from './mobile-detect'

export default {
	init() {
		
		//Here we will initialize fullpage js and swiper. As soon as the swiper panel becomes active, fullpage js scroll is disabled the slider
		//scroll can run. Once the end of the slider is reached, fullpage js is enabled again
		
		const transitionSpeed = 800
		var scrollAllowUp = true //Used to keep of mobile swipe up and down ability
		var scrollAllowDown = true //Used to keep of mobile swipe up and down ability

		let mySwiper = new Swiper('.js-our-work-slider', {
			modules: [EffectCreative, Navigation, Autoplay, Mousewheel],
            autoplay: false,
            // Navigation arrows
            navigation: {
                nextEl: '.swiper-button-next',
                prevEl: '.swiper-button-prev',
            },
			mousewheel: {
				releaseOnEdges: true,
            },
			slidesPerView: 'auto',
			centeredSlides: true,
			spaceBetween: 0,
			speed: transitionSpeed,
			grabCursor: true,
			centeredSlides: false,
			effect: "creative",
			creativeEffect: {
				prev: {
					shadow: false,
					translate: ["0%", 0, -1],
				},
				next: {
					translate: ["100%", 0, 0],
				},
			},
			on: {
				slideChangeTransitionEnd: function (swiper) {
					var slideTextContainers, activeSlideText, navigationNextArrow, swiperSlide, activeSlide

					// slideTextContainers = document.querySelectorAll('.js-slide-text')
                    swiperSlide = document.querySelectorAll('.swiper-slide');
					activeSlideText = document.querySelector('.js-slide-text-' + mySwiper.activeIndex)
					activeSlide = document.querySelector('.js-swiper-slide-'+ mySwiper.activeIndex)


					swiperSlide.forEach(swiperSlide => {
                        swiperSlide.classList.remove('md:rounded-l-lg');
                        swiperSlide.classList.add('md:rounded-l-lg');
                    });

                    if (activeSlide) {
                        activeSlide.classList.remove('md:rounded-l-lg');
					}
					
					//Re enable upward vertical scrolling when we reach the start of the slider so we can exit
					if (fullPageInstance && mySwiper.isBeginning) {
						scrollAllowUp = true
						fullPageInstance.setAllowScrolling(true, 'up')
					}

					//Re enable downward vertical scrolling when we reach the start of the slider so we can exit
					if (fullPageInstance && mySwiper.isEnd) {
						scrollAllowDown = true
						fullPageInstance.setAllowScrolling(true, 'down')
					}
				},
				slideChangeTransitionStart: function (swiper) {
                    let activeSlide;

                    activeSlide = document.querySelector('.js-swiper-slide-'+ mySwiper.activeIndex)

                    activeSlide.classList.add('md:rounded-l-lg')
					
					//prevent any vertical scrolling once we're in the swiper slider, ie not the first or last slides
					if (fullPageInstance && (mySwiper.activeIndex == 1 || mySwiper.activeIndex == mySwiper.slides.length - 2)) {
						scrollAllowDown = false
						scrollAllowUp = false
						fullPageInstance.setAllowScrolling(false)
						swiper.mousewheel.enable()
					};
                },
			},
		});

		const mainContainer = document.querySelector('.js-main')
		const autoAdvanceFirstSlide = document.querySelector('.js-our-work-slider').dataset.autoadvance
		var actualHeight
		var elementHeight
		var barHeight
		var innerBarHeight

		var fullPageInstance = new fullpage('#fullpage', {
			licenseKey: 'gplv3-license',
			fadingEffect: true,
			scrollingSpeed: transitionSpeed,
			onLeave: function (origin, destination, direction, trigger) {
				
				//Sometimes scrolling will be disabled when exiting the swiper panel, this is to re enable it
				if (destination.index !== 1) {
					scrollAllowDown = true
					scrollAllowUp = true
					fullpage_api.setAllowScrolling(true)
				}
		

				if (destination.index === 1) {
					

					//Disable fullpage (vertical scrolling while we are in the slider)
					if (mySwiper.isEnd) {
						//If we are at the end of the slider, re enable vertical scrolling for down only, so we can exit the swiper panel
						setTimeout(() => {
							scrollAllowDown = true
							scrollAllowUp = false
						}, transitionSpeed)
						fullpage_api.setAllowScrolling(false, 'up')
					}
					
					if (!mySwiper.isBeginning && !mySwiper.isEnd) {
						//Ensure we can still scroll up to exit the panel when we land on the swiper panel for the first time
						setTimeout(() => {
							scrollAllowUp = true
							scrollAllowDown = false
						}, transitionSpeed)
						
						fullpage_api.setAllowScrolling(false, 'down')
					}

					//if we arrive on the slider page and the slider is at position 0, automatically slide in the first slide
					if (mySwiper.isBeginning && autoAdvanceFirstSlide) {
						mySwiper.mousewheel.disable()
						setTimeout(() => {
							mySwiper.slideNext()
						}, transitionSpeed)
					}
					
					if (mySwiper.isEnd) {
						mySwiper.mousewheel.disable()
						setTimeout(() => {
							mySwiper.mousewheel.enable()
						}, transitionSpeed)
					}
				}

				if (destination.index === 0) {
					mainContainer.style.marginTop = `${barHeight}px`
				}
				else {
					if (direction === 'up') {
						if (mobileDetect.detectBrowser() === 'Chrome' || mobileDetect.detectBrowser() === 'Safari') {
							mainContainer.style.marginTop = `${barHeight / 2}px`
						}
						else {
							mainContainer.style.marginTop = 0
						}
					}
					else {
						mainContainer.style.marginTop = `${innerBarHeight}px`
					}
					
				}
			},
			afterRender: function () {
				mobileHeroFader.init()
				//account for mobile addressbar which hides our navigation bar
				actualHeight = window.innerHeight
				elementHeight = document.querySelector('.js-main').clientHeight
				barHeight = 0
				innerBarHeight = 0

				if (mobileDetect.detectBrowser() === 'Chrome' || mobileDetect.detectBrowser() === 'Safari') {
					barHeight = (elementHeight - actualHeight) * 2
				}

				if (mobileDetect.detectBrowser() === 'Firefox') {
					barHeight = elementHeight - actualHeight
					innerBarHeight = -56
				}

				// if (destination.index === 0) {
					mainContainer.style.marginTop = `${barHeight}px`
				// }
			},
			afterLoad: function(origin, destination, direction, trigger){
				//Sometimes scrolling will be disabled when exiting the swiper panel, this is to re enable it
				if (destination.index !== 1) {
					scrollAllowDown = true
					scrollAllowUp = true
					fullpage_api.setAllowScrolling(true)
				}
			}
		});


		// Control ability to swipe up and down on mobile
		// Variables to store initial touch position
		let startY = 0;
		let endY = 0;

		// Event listener for touchstart
		document.addEventListener("touchstart", function(event) {
			startY = event.touches[0].clientY
		});

		// Event listener for touchend
		document.addEventListener("touchend", function(event) {
			endY = event.changedTouches[0].clientY

			// Calculate the distance swiped
			let distance = endY - startY

			// Check if the swipe distance is above a certain threshold
			if (distance > 50 && !scrollAllowUp && !mySwiper.animating) {
				mySwiper.slidePrev()
			} else if (distance < -50 && !scrollAllowDown && !mySwiper.animating) {
				mySwiper.slideNext()
			}
		});

	},
}
