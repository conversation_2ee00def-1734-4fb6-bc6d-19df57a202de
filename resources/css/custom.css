/* If you don't want to use the JS in CSS syntax from `tailwind.config.site.js` you can add your own custom CSS here. Use different layers to make sure your CSS either get's purged or not: https://tailwindcss.com/docs/functions-and-directives#layer. CSS in the base layer won't get purged. */

@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap');

@layer base {
	body {
		/* cursor: url(/public/visueals/cursor.png), auto; */
	}
}

@layer components {
	/* .home::-webkit-scrollbar {
		display: none
	} */

	/* Home page mobile hero fade effect */
	.js-mobile-hero-fade.active .js-mobile-hero-fade-target {
		z-index: 11;
		opacity: 1;
	}

	.c-label {
		transform: skew(-15deg, 0deg);
		@apply inline-block bg-accent-600 mr-8 relative w-3 sm:w-[15px] lg:w-[21px] h-[70px] sm:h-[92px] lg:h-[125px] top-[7px] sm:top-[14px]
	}

	.c-label.--white {
		background-color: transparent;
		@apply border-4 border-white;
	}

	/* Marquee styles */
	.marquee {
		--gap: 16px;
	}

	@keyframes scroll {
		from {
			transform: translateX(0);
		}

		to {
			transform: translateX(calc(-100% - var(--gap)));
		}
	}

	/* Pause animation when reduced-motion is set */
	@media (prefers-reduced-motion: reduce) {
		.marquee__content {
			animation-play-state: paused !important;
		}
	}

	/* Enable animation */
	.marquee__content {
		animation: scroll 180s linear infinite;
	}

	/* Reverse animation */
	.marquee--reverse .marquee__content {
		animation: scroll 180s linear infinite;
		animation-direction: reverse;
	}
}

@layer utilities {
	.strikethrough {
		position: relative;
	}

	.strikethrough:after {
		content: '';
		position: absolute;
		top: 50%;
		left: 0;
		right: 0;
		height: 33%;
		transform: translatey(-50%);
		background: theme(colors.accent.600);
	}
}

input:checked + label {
	@apply bg-dark;
	@apply text-white;
}
input:checked + label svg {
	@apply block;
}

.slider-navigation-arrows {
    stroke: black;
    height: 80px;
}

.swiper-button-disabled {
    opacity: 0;
}

.fp-watermark {
	display: none !important;
}

.aspect-video {
	aspect-ratio: 16/9;
}

[x-cloak] {
	display: none !important;
}

.swiper-button-disabled {
	@apply !opacity-[0.3]
}

.swiper-pagination {
	@apply flex gap-3 justify-center mt-6
}

.swiper-pagination-bullet {
	@apply w-[26px] h-2 rounded-sm bg-grey-100 transition-all duration-300 ease-linear cursor-pointer
}

.swiper-pagination-bullet.swiper-pagination-bullet-active {
	@apply w-12 bg-accent
}

dialog::backdrop {
	background-color: rgba(0,0,0,0.5);
}

.js-random-term .char {
	display: inline-block;
	transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  }
  
  .js-random-term .char.splitting-out {
	opacity: 0;
	transform: translateY(-15px);
  }
  
  .js-random-term .char.splitting-in {
	opacity: 0;
	transform: translateY(15px);
  }
  
  .js-random-term {
	white-space: nowrap;
	display: inline-block;
  }