tabs:
  main:
    display: Main
    sections:
      -
        fields:
          -
            handle: background_image
            field:
              mode: list
              container: images
              folder: services
              restrict: true
              allow_uploads: true
              show_filename: true
              show_set_alt: true
              type: assets
              display: Background
              icon: assets
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
          -
            handle: title
            field:
              type: text
              required: true
              validate:
                - required
          -
            handle: header_description
            field:
              antlers: false
              type: textarea
              display: 'Header description'
              icon: textarea
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
          -
            handle: listing_image
            field:
              use_breakpoints: false
              allow_ratio: false
              allow_fit: true
              breakpoints:
                - sm
                - md
                - lg
                - xl
                - 2xl
              container: images
              folder: services
              restrict: true
              allow_uploads: true
              type: responsive
              display: 'Listing image'
              icon: assets
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
          -
            handle: listing_description
            field:
              antlers: false
              type: textarea
              display: 'Listing description'
              icon: textarea
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
              validate:
                - required
          -
            handle: detail_caption_line_1
            field:
              input_type: text
              antlers: false
              type: text
              display: 'Detail caption line 1'
              icon: text
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
              validate:
                - required
          -
            handle: detail_caption_line_2
            field:
              input_type: text
              antlers: false
              type: text
              display: 'Detail caption line 2'
              icon: text
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
              validate:
                - required
          -
            handle: our_thinking_cards
            field:
              fields:
                -
                  handle: title
                  field:
                    input_type: text
                    antlers: false
                    type: text
                    display: Title
                    icon: text
                    listable: hidden
                    instructions_position: above
                    visibility: visible
                    hide_display: false
                    validate:
                      - required
                -
                  handle: tag
                  field:
                    max_items: 1
                    mode: default
                    create: false
                    taxonomies:
                      - tags
                    type: terms
                    display: Tag
                    icon: taxonomy
                    listable: hidden
                    instructions_position: above
                    visibility: visible
                    hide_display: false
              mode: table
              reorderable: true
              fullscreen: true
              type: grid
              display: 'Our thinking cards'
              icon: grid
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
          -
            handle: featured_case_studies
            field:
              mode: default
              create: false
              collections:
                - case_studies
              type: entries
              display: 'Featured case studies'
              icon: entries
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
          -
            handle: clients
            field:
              type: section
              display: Clients
              icon: section
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
          -
            handle: image_marqee_rows
            field: trusted_partners.image_marqee_rows
          -
            handle: faqs
            field:
              fields:
                -
                  handle: question
                  field:
                    input_type: text
                    antlers: false
                    type: text
                    display: Question
                    icon: text
                    listable: hidden
                    instructions_position: above
                    visibility: visible
                    hide_display: false
                    validate:
                      - required
                -
                  handle: answer
                  field:
                    input_type: text
                    antlers: false
                    type: text
                    display: Answer
                    icon: text
                    listable: hidden
                    instructions_position: above
                    visibility: visible
                    hide_display: false
                    validate:
                      - required
              mode: table
              reorderable: true
              fullscreen: true
              type: grid
              display: FAQs
              icon: grid
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
          -
            handle: listing_images
            field:
              min_files: 1
              mode: list
              container: images
              folder: services
              restrict: true
              allow_uploads: true
              show_filename: true
              show_set_alt: true
              type: assets
              display: 'Listing images'
              icon: assets
              listable: hidden
              instructions_position: above
              visibility: visible
              hide_display: false
  sidebar:
    display: Sidebar
    sections:
      -
        fields:
          -
            handle: slug
            field:
              type: slug
              localizable: true
          -
            handle: tags
            field:
              type: terms
              taxonomies:
                - tags
              display: Tags
              mode: select
title: Service
