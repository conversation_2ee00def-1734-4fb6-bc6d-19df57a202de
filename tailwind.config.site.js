//--------------------------------------------------------------------------
// Tailwind site configuration
//--------------------------------------------------------------------------
//
// Use this file to completely define the current sites design system by
// adding and extending to Tailwinds default utility classes.
//

const defaultTheme = require('tailwindcss/defaultTheme')
const plugin = require('tailwindcss/plugin')
const colors = require('tailwindcss/colors')

// Define colors separately so we can use them for safelist generation
const siteColors = {
	dark: '#16151A',
	white: '#ffffff',
	black: '#000000',
	// Primary: primary brand color with a default fallback if you don't need shades. Always set a DEFAULT when you use shades.
	primary: {
		DEFAULT: '#16151A'
	},
	secondary: {
		DEFAULT: '#FF0025'
	},
	accent: {
		'050' : '#FFDBE0',
		'100' : '#FFB9C3',
		'200' : '#FF7C8F',
		'300' : '#FF7C8F',
		'400' : '#FC546C',
		'500' : '#FF2A49',
		DEFAULT : '#FF0025',
		'600' : '#FF0025',
		'700' : '#CA001D',
		'800' : '#8F0000',
		'900' : '#540000',
	},
	grey: {
		'050' : '#F1F1F1',
		'100' : '#E8E8E8',
		DEFAULT : '#E8E8E8',
		'200' : '#D4D4D4',
		'300' : '#C5C5C5',
		'400' : '#B3B3B3',
		'500' : '#8B8A8D',
		'600' : '#5C5C5F',
		'700' : '#454448',
		'800' : '#2E2D31',
		'900' : '#16151A',
	},
	info: {
		'050' : '#DFEAF4',
		'100' : '#C0D6EC',
		'200' : '#A5C9EE',
		'300' : '#86B6E5',
		'400' : '#5DA4EA',
		'500' : '#3F8EDD',
		DEFAULT : '#3F8EDD',
		'600' : '#1F78D1',
		'700' : '#1963AD',
		'800' : '#17497B',
		'900' : '#0E3257',
	},
	error: {
		'050' : '#FFE0DB',
		'100' : '#FFC4BB',
		'200' : '#FFADA0',
		'300' : '#FF8976',
		'400' : '#FD7862',
		'500' : '#FA5C43',
		'600' : '#EC4125',
		DEFAULT : '#EC4125',
		'700' : '#DA3318',
		'800' : '#B72811',
		'900' : '#861E0D',
	},
	success: {
		'050' : '#E9F9EF',
		'100' : '#D3F3E0',
		'200' : '#ADE7C4',
		'300' : '#81E1A9',
		'400' : '#6CD397',
		'500' : '#40B971',
		'600' : '#1AAA55',
		DEFAULT : '#1AAA55',
		'700' : '#148743',
		'800' : '#0B6530',
		'900' : '#093F1F',
	},
	// Neutrals: neutral colors, with a default fallback if you don't need shades. Always set a DEFAULT when you use shades.
	neutral: {
		DEFAULT: '#16151A',
	},
	iclei: {
		bold: '#0B4C52',
		accent: '#FDE244',
		surface: '#97B4B9',
	},
	cwn: {
		bold: '#238A86',
		accent: '#F9BD39',
		surface: '#91C4C2',
	},
	joshu: {
		bold: '#F35638',
		accent: '#ECAA00',
		surface: '#604C99',
	},
	dineplan: {
		bold: '#EB454F',
		accent: '#00DEC5',
		surface: '#212945',
	},
	skinrenewal: {
		bold: '#CFB62C',
		accent: '#005E5E',
		surface: '#F0EFEF',
	},
	skinrenewal_historical: {
		bold: '#CEDFB5',
		accent: '#A1BD76',
		surface: '#F8F9FB',
	},
	brightplan: {
		bold: '#6094C7',
		accent: '#FCD336',
		surface: '#F0F7FC',
	},
	nac: {
		bold: '#BF3323',
		accent: '#0C3246',
		surface: '#FAFAF4',
	},
	farosian: {
		bold: '#012131',
		accent: '#03AF9C',
		surface: '#02776F',
	},
	okumhlophe: {
		bold: '#78D64B',
		accent: '#FFD200',
		surface: '#000000',
	},
	valenture: {
		bold: '#000000',
		accent: '#CEB537',
		surface: '#F5F5F5',
	},
	pmi: {
		bold: '#348C45',
		accent: '#4DB85C',
		surface: '#EAE7E4',
	},
	ramp: {
		bold: '#000823',
		accent: '#FC1165',
		surface: '#F5F8FF',
	},
	duram: {
		bold: '#0E70CC',
		accent: '#FB0021',
		surface: '#041E42',
	},
	n3yh: {
		bold: '#CCA085',
		accent: '#999B84',
		surface: '#F7ECDF',
	},
	miss_lyn: {
		bold: '#9DB1A3',
		accent: '#E3CDBB',
		surface: '#CCC6BE',
	},
	cattlemans: {
		bold: '#000000',
		accent: '#A9663F',
		surface: '#474B4A',
	},
	beachhouse: {
		bold: '#138DCA',
		accent: '#FA729C',
		surface: '#8AD7E2',
	},
	streetcaster: {
		bold: '#000000',
		accent: '#F9FF62',
		surface: '#868B87',
	},
	ribshack: {
		bold: '#DA3731',
		accent: '#DFB253',
		surface: '#000000',
	},
	relycomply: {
		bold: '#000000',
		accent: '#1EC000',
		surface: '#D5D0CA',
	},
	pmi: {
		bold: '#348C45',
		accent: '#4DB85C',
		surface: '#EAE7E4',
	},
	polyco: {
		bold: '#41BB52',
		accent: '#FCDD00',
		surface: '#18988B',
	},
	easycar: {
		bold: '#0A214F',
		accent: '#FF5C54',
		surface: '#4070FF',
	},
	bae: {
		bold: '#FF002A',
		accent: '#F6C000',
		surface: '#1A2426',
	},
	blonde_chaos: {
		bold: '#EBE3DF',
		accent: '#E8D488',
		surface: '#E7DFD4',
	},
	lovepup: {
		bold: '#443F6E',
		accent: '#E1523D',
		surface: '#F4BBCA',
	},
	nua: {
		bold: '#F2582A',
		accent: '#9E08A6',
		surface: '#FFB000',
	},
	remy_martin: {
		bold: '#BC9367',
		accent: '#252122',
		surface: '#FFB000',
	},
	glenfiddich: {
		bold: '#11290f',
		accent: '#352214',
		surface: '#F4F4F4',
	},
	russian_bear: {
		bold: '#DD0C32',
		accent: '#121212',
		surface: '#F4F4F4',
	},
	mtvernon: {
		bold: '#1a2b3bff',
		accent: '#b69337',
		surface: '#F4F4F4',
	},
	gettod: {
		bold: '#597e99',
		accent: '#ef616c',
		surface: '#D0E9F5',
	},
	jive: {
		bold: '#1e8aff',
		accent: '#EA0029',
		surface: '#FAAF40',
	},
	rtd: {
		bold: '#101125',
		accent: '#16AAB9',
		surface: '#EF175E',
	},
	continuon: {
		bold: '#F8542E',
		accent: '#252122',
		surface: '#F9F7F8',
	},
	bmw: {
		bold: '#040404',
		accent: '#0066B1',
		surface: '#BDBDBD',
	},
	aesthetic_connection: {
		bold: '#FF3131',
		accent: '#212529',
		surface: '#D4D5D5',
	},
	bookamat: {
		bold: '#55CAF1',
		accent: '#74b3c5',
		surface: '#868E91',
	},
	dineplan: {
		bold: '#212945',
		accent: '#EB454F',
		surface: '#00DEC5',
	},
	filmer: {
		bold: '#0DAEEF',
		accent: '#ED185B',
		surface: '#000000',
	},
};

// Generate safelist based on colors
const generateSafelist = () => {
	// Start with any static classes we always want to include
	const staticSafelist = ['aspect-video'];

	// Generate the utility classes for each brand color
	const colorUtilities = [];

	// Explicitly add the problematic classes
	colorUtilities.push('bg-nua-bold');
	colorUtilities.push('bg-nua-accent');
	colorUtilities.push('hover:text-nua-accent');
	colorUtilities.push('bg-lovepup-bold');
	colorUtilities.push('bg-lovepup-accent');
	colorUtilities.push('hover:text-lovepup-accent');

	// Loop through all color definitions
	Object.entries(siteColors).forEach(([colorName, colorValue]) => {
		// Skip the ones we've already added explicitly
		if (colorName === 'nua' || colorName === 'lovepup') {
			return;
		}

		// Check if this is an object with 'accent' or 'bold' properties
		if (typeof colorValue === 'object' &&
			!Array.isArray(colorValue) &&
			colorValue !== null) {

			// Add background classes
			if (colorValue.accent !== undefined) {
				colorUtilities.push(`bg-${colorName}-accent`);
			}
			if (colorValue.bold !== undefined) {
				colorUtilities.push(`bg-${colorName}-bold`);
			}

			// Add hover text classes
			if (colorValue.accent !== undefined) {
				colorUtilities.push(`hover:text-${colorName}-accent`);
			}
		}
	});

	// Return the combined safelist
	return [...staticSafelist, ...colorUtilities];
};

module.exports = {
	presets: [],
	theme: {
		container: {
			center: true,
		},
		// Here we define the default colors available. If you want to include
		// all default Tailwind colors you should extend the colors instead.
		colors: siteColors,
		extend: {
			// Set default transition durations and easing when using the transition utilities.
			transitionDuration: {
				DEFAULT: '300ms',
			},
			transitionTimingFunction: {
				DEFAULT: 'cubic-bezier(0.4, 0, 0.2, 1)',
			},
			maxWidth: {
				'1/2': '50%',
			},
			height: {
				'full-less-nav': 'calc(100vh - 82px)',
			}
		},
		// Remove the font families you don't want to use.
		fontFamily: {
			mono: [
				// Use a custom mono font for this site by changing 'Anonymous' to the
				// font name you want and uncommenting the following line.
				// 'Anonymous',
				...defaultTheme.fontFamily.mono,
			],
			sans: [
				// Use a custom sans serif font for this site by changing 'Gaultier' to the
				// font name you want and uncommenting the following line.
				'Poppins',
				...defaultTheme.fontFamily.sans,
			],
			serif: [
				// Use a custom serif font for this site by changing 'Lavigne' to the
				// font name you want and uncommenting the following line.
				// 'Lavigne',
				...defaultTheme.fontFamily.serif,
			],
		},
		// The font weights available for this site.
		fontWeight: {
			// hairline: 100,
			// thin: 200,
			// light: 300,
			normal: 400,
			// medium: 500,
			semibold: 600,
			bold: 700,
			// extrabold: 800,
			// black: 900,
		},
		screens: {
			'sm': '640px',
			'md': '768px',
			'lg': '1024px',
			'xl': '1325px',
			'2xl': '1776px',
		},
		boxShadow: {
			'sm': [
				'0 0 1px rgba(12, 26, 75, 0.15)',
				'0 4px 20px -2px rgba(50, 50, 71, 0.08)'
			],
			'md': [
				'0 0 1px rgba(12, 26, 75, 0.1)',
				'0 10px 16px rgba(20, 37, 63, 0.06)'
			],
			'lg': [
				'0 0 1px rgba(12, 26, 75, 0.1)',
				'0 20px 24px rgba(20, 37, 63, 0.06)'
			],
			'xl': [
				'0 0 1px rgba(12, 26, 75, 0.1)',
				'0 30px 40px rgba(20, 37, 63, 0.08)'
			],
			'fill-dark': [
				'0px -40px 0px 0px #16151A'
			],
			'fill-grey-100': [
				'0px -40px 0px 0px #E8E8E8'
			],
			'card': [
				'0px 4px 98px rgba(0, 0, 0, 0.13), 12px 12px 0px #FFFFFF',
				'24px 24px 0px #EFEFEF, 0px 4px 98px rgba(0, 0, 0, 0.13)'
			],
			'card-tight': [
				'0px 4px 98px rgba(0, 0, 0, 0.13), 6px 6px 0px #FFFFFF',
				'12px 12px 0px #EFEFEF, 0px 4px 98px rgba(0, 0, 0, 0.13)'
			]
		},
		spacing: {
			'0': '0',
			'1': '4px',
			'2': '8px',
			'3': '12px',
			'4': '16px',
			'5': '24px',
			'6': '32px',
			'7': '40px',
			'8': '48px',
			'9': '56px',
			'10': '64px',
			'11': '80px',
			'12': '96px',
			'13': '112px',
			'14': '128px',
			'15': '144px',
			'16': '160px',
			'17': '176px',
			'18': '192px',
			'19': '208px',
			'20': '260px',
		},
		borderRadius: {
			'none': '0',
			'sm': '8px',
			'md': '24px',
			'lg': '40px',
			'full': '50%',
		},
		fontSize: {
			'12': ['12px', '1.2'],
			'14': ['14px', '1.2'],
			'16': ['16px', '1.2'],
			'primary': ['16px', '1.2'],
			'18': ['18px', '1.2'],
			'24': ['24px', '1.2'],
			'32': ['32px', '1.2'],
			'36': ['36px', '1.2'],
			'48': ['48px', '1.2'],
			'56': ['56px', '1.2'],
			'64': ['64px', '1.2'],
			'112': ['112px', '1.2'],
			'210': ['210px', '1.2'],
			'hero-sub': ['clamp(25px, 1.8vw, 35px)', '1.2'],
			'hero-contained': ['clamp(80px, 9.7vw, 90px)', '1.2'],
			'hero': ['clamp(80px, 9.7vw, 210px)', '1.2']
		},
	},
	plugins: [
		plugin(function({ addBase, theme }) {
			addBase({
				// Default color transition on links unless user prefers reduced motion.
				'@media (prefers-reduced-motion: no-preference)': {
				'a': {
					transition: 'color .3s ease-in-out',
				},
				},
				'html': {
					color: theme('colors.neutral.DEFAULT'),
					//--------------------------------------------------------------------------
					// Set sans, serif or mono stack with optional custom font as default.
					//--------------------------------------------------------------------------
					// fontFamily: theme('fontFamily.mono'),
					fontFamily: theme('fontFamily.sans'),
					// fontFamily: theme('fontFamily.serif'),
				},
				'mark': {
					backgroundColor: theme('colors.primary.DEFAULT'),
					color: theme('colors.white')
				},
			})
		}),

		// Custom components for this particular site.
		plugin(function({ addComponents, theme }) {
			const components = {
			}
			addComponents(components)
		}),

		// Custom utilities for this particular site.
		plugin(function({ addUtilities, theme, variants }) {
			const newUtilities = {
			}
			addUtilities(newUtilities)
		}),

		//Add translate Z utility
		plugin(function({ matchUtilities, theme }) {
			matchUtilities(
				{
					'translate-z': (value) => ({
						'--tw-translate-z': value,
						transform: ` translate3d(var(--tw-translate-x), var(--tw-translate-y), var(--tw-translate-z)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))`,
					}),
				},
				{
					values: theme('translate')
				}
			)
		}),

		plugin(function({ addVariant }) {
			addVariant('fullscreen', '&:fullscreen');
		})
	],
	// Generate safelist programmatically based on theme colors
	safelist: generateSafelist()
}
