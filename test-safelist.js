// Test script to verify the safelist generation
const siteColors = {
  lovepup: {
    bold: '#443F6E',
    accent: '#E1523D',
    surface: '#F4BBCA',
  },
  nua: {
    bold: '#F2582A',
    accent: '#9E08A6',
    surface: '#FFB000',
  }
};

// Generate safelist based on colors
const generateSafelist = () => {
  // Start with any static classes we always want to include
  const staticSafelist = ['aspect-video'];
  
  // Generate the utility classes for each brand color
  const colorUtilities = [];
  
  // Loop through all color definitions
  Object.entries(siteColors).forEach(([colorName, colorValue]) => {
    // Check if this is an object with 'accent' or 'bold' properties
    if (typeof colorValue === 'object' &&
      !Array.isArray(colorValue) &&
      colorValue !== null) {
      
      // Add background classes
      if (colorValue.accent !== undefined) {
        colorUtilities.push(`bg-${colorName}-accent`);
      }
      if (colorValue.bold !== undefined) {
        colorUtilities.push(`bg-${colorName}-bold`);
      }
      
      // Add hover text classes
      if (colorValue.accent !== undefined) {
        colorUtilities.push(`hover:text-${colorName}-accent`);
      }
    }
  });
  
  // Return the combined safelist
  return [...staticSafelist, ...colorUtilities];
};

const safelist = generateSafelist();
console.log('Safelist includes the following classes:');
console.log(safelist.join('\n'));

// Specifically check for the problematic classes
console.log('\nSpecifically checking for the problematic classes:');
console.log('bg-nua-bold included:', safelist.includes('bg-nua-bold'));
console.log('bg-lovepup-bold included:', safelist.includes('bg-lovepup-bold'));
